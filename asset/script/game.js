cc.Class({
    extends: cc.Component,

    properties: {
        rope_node:{
            default:null,
            type:cc.Node,
            tooltip: '设置绳子节点'
        },
        cow_ins:{
            default:null,
            type:cc.Node,
            tooltip:'设置牛节点'
        },
        rope_imgs:{
            default:[],
            type:cc.SpriteFrame,
            tooltip:'设置绳子不同情况下（是否套到牛、套到哪种牛）的相应图片'
        },
        cow_prefab:{
            default:null,
            type:cc.Prefab,
            tooltip:'牛对象预制体（可用于新建一个牛）'
        },
        /*time:{
            default:30,
            tooltip:'设置游戏时长（单位为秒）'
        },
        contentLabel:{
            default:[],
            type:cc.Label.toString(),
            tooltip:"设置不同得分对应的称号"
        }*/

    },

    // LIFE-CYCLE CALLBACKS:

    onLoad () {
        this.isFirst=true;
        this.success=false;
        //初始分数
        this.scoreNum=0;
        //console.log('微信开发666');
    },

    start () {
        cc.director.pause();
        let countDownLabel=cc.find("Canvas/bg_sprite/count_down").getComponent(cc.Label);
        let time=30;
        countDownLabel.string=time+'s';
        this.schedule(function (){
            time--;
            countDownLabel.string=time+'s';
            cc.log(time);
            if(time<=10){
                countDownLabel.node.color=cc.Color.RED;
            }
            if(time===0){
                //获取弹窗节点
                let resultNode=cc.find("Canvas/result");
                //获取title和content节点
                let titleNode=resultNode.getChildByName("title");
                let contentNode=resultNode.getChildByName("content");

                //展示分数
                titleNode.getComponent(cc.Label).string="最终得分"+this.scoreNum;

                //获取组件
                let contentLabel=contentNode.getComponent(cc.Label);
                switch(true){
                    case this.scoreNum<=5:
                       contentLabel.string="套牛小白";
                       break;
                    case this.scoreNum<=12:
                        contentLabel.string="套牛高手";
                        break;
                    default:
                        contentLabel.string="套牛王者";
                        break;
                }
                resultNode.active=true;
                cc.director.pause();
            }
        },1);
    },

    //update (dt) {},

    clickCapture:function(event,customEventDate){
        this.rope_node.active=true;
        //设置绳子在当前父节点的顺序
        this.rope_node.setSiblingIndex(100);
        //设置绳子起始位置
        this.rope_node.y=-470;
        //向上移动
        const up=cc.moveTo(0.5,this.rope_node.x,0);//三个参数：0.5s移动到x,y位置


        //判定结果
        let result= cc.callFunc(function(){
            //当前牛的x轴坐标
            let current=this.cow_ins.x;
            if(current>-30&&current<30&&this.isFirst||current>70&&current<130&&(!this.isFirst)){
                this.isFirst=false;
                cc.log("捕捉成功！");
                //移出
                let bgNode=this.node.getChildByName("bg_sprite");
                bgNode.removeChild(this.cow_ins);
                //更换绳子
                let ropeType=this.cow_ins.getComponent("cow").randomType+1;
                this.rope_node.getComponent(cc.Sprite).spriteFrame=this.rope_imgs[ropeType];
                //生成新的牛节点
                this.cow_ins=cc.instantiate(this.cow_prefab);
                this.cow_ins.y=0;
                bgNode.addChild(this.cow_ins);
                //捕捉成功，得分加一
                this.success=true;
                this.scoreNum+=1;
            }else{
                cc.log('捕捉失败！');
                this.success=false;
                //cc.log(current,isFirst);
            }
        },this);

        //拉回来
        let down=cc.moveTo(0.8,this.rope_node.x,-600);

        let finish=cc.callFunc(function(){
            this.rope_node.getComponent(cc.Sprite).spriteFrame=this.rope_imgs[0];
            if(this.success===true){
                let scoreLabel=cc.find("Canvas/bg_sprite/score").getComponent(cc.Label);
                scoreLabel.string="Score:"+this.scoreNum;
            }
        },this);

        //定义一个序列动画
        let sequence=cc.sequence(up,result,down,finish);
        this.rope_node.runAction(sequence);

    },
    //关闭按钮，继续游戏
    closeBtn(){
        cc.log('继续游戏');
        cc.director.resume();
        cc.director.loadScene("game");
    },
    startBtn(){
        let startBtnNode=cc.find("Canvas/start");
        startBtnNode.active=false;
        cc.director.resume();
    }
});
